//
//  ContentView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

struct ContentView: View {
    @StateObject private var locationManager = LocationManager()
    @State private var showingLocationAlert = false
    @State private var bottomSheetOffset: CGFloat = 300 // Initial offset (mostly hidden)
    @State private var isDragging = false

    // Bottom sheet positions
    private let minOffset: CGFloat = 100 // Fully expanded
    private let maxOffset: CGFloat = 300 // Mostly collapsed
    private let snapThreshold: CGFloat = 50 // Threshold for snapping

    var body: some View {
        NavigationStack {
            ZStack {
                // Main Map View with custom user location
                MapViewRepresentable(locationManager: locationManager)
                    .ignoresSafeArea()
                    .onAppear {
                        // LocationManager will automatically start location updates in init
                        // This is just a fallback in case user manually requests location
                        if locationManager.authorizationStatus == .authorizedWhenInUse ||
                           locationManager.authorizationStatus == .authorizedAlways {
                            locationManager.requestLocation()
                        }
                    }

                // Top Navigation Bar
                VStack {
                    HStack {
                        Text("XYZ Melbourne Parking")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color(.systemBackground).opacity(0.9))
                            .clipShape(RoundedRectangle(cornerRadius: 12))

                        Spacer()

                        // Location Button
                        Button(action: {
                            locationManager.requestLocation()
                        }) {
                            Image(systemName: "location.fill")
                                .font(.title3)
                                .foregroundColor(.blue)
                        }
                        .padding(12)
                        .background(Color(.systemBackground))
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    Spacer()
                }

                // Bottom Sheet
                GeometryReader { geometry in
                    VStack {
                        Spacer()

                        BottomSheetView(
                            offset: $bottomSheetOffset,
                            isDragging: $isDragging,
                            minOffset: minOffset,
                            maxOffset: maxOffset,
                            snapThreshold: snapThreshold,
                            screenHeight: geometry.size.height
                        )
                    }
                }
            }
        }
        .alert("Location Access Required", isPresented: $showingLocationAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable location access in Settings to find parking near you.")
        }
        .onChange(of: locationManager.authorizationStatus) { _, status in
            if status == .denied || status == .restricted {
                showingLocationAlert = true
            }
        }
    }
}

#Preview {
    ContentView()
}

// MARK: - MapView UIViewRepresentable
struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var locationManager: LocationManager

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = false // We'll handle this ourselves
        mapView.userTrackingMode = .none
        mapView.showsCompass = true
        mapView.showsScale = true

        // Register custom annotation view
        mapView.register(UserLocationAnnotationView.self,
                        forAnnotationViewWithReuseIdentifier: UserLocationAnnotationView.identifier)

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update map region
        if mapView.region.center.latitude != locationManager.region.center.latitude ||
           mapView.region.center.longitude != locationManager.region.center.longitude {
            mapView.setRegion(locationManager.region, animated: true)
        }

        // Update user location annotation
        if let userAnnotation = locationManager.userLocationAnnotation {
            if !mapView.annotations.contains(where: { $0 === userAnnotation }) {
                mapView.addAnnotation(userAnnotation)
            }

            // Update heading for existing annotation view
            if let annotationView = mapView.view(for: userAnnotation) as? UserLocationAnnotationView {
                annotationView.updateHeading(locationManager.userHeading)
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewRepresentable

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            if annotation is UserLocationAnnotation {
                let annotationView = mapView.dequeueReusableAnnotationView(
                    withIdentifier: UserLocationAnnotationView.identifier,
                    for: annotation
                ) as! UserLocationAnnotationView

                // Update heading immediately
                annotationView.updateHeading(parent.locationManager.userHeading)

                return annotationView
            }
            return nil
        }

        func mapView(_ mapView: MKMapView, didAdd views: [MKAnnotationView]) {
            // Update heading for newly added user location annotation
            for view in views {
                if let userLocationView = view as? UserLocationAnnotationView {
                    userLocationView.updateHeading(parent.locationManager.userHeading)
                }
            }
        }
    }
}

// MARK: - Bottom Sheet View
struct BottomSheetView: View {
    @Binding var offset: CGFloat
    @Binding var isDragging: Bool

    let minOffset: CGFloat
    let maxOffset: CGFloat
    let snapThreshold: CGFloat
    let screenHeight: CGFloat

    var body: some View {
        VStack(spacing: 0) {
            // Handle bar
            RoundedRectangle(cornerRadius: 3)
                .fill(Color.gray.opacity(0.4))
                .frame(width: 40, height: 6)
                .padding(.top, 8)

            // Content
            VStack(spacing: 16) {
                // Header
                HStack {
                    Text("附近停车场")
                        .font(.title2)
                        .fontWeight(.bold)

                    Spacer()

                    Button(action: {
                        // Search action
                    }) {
                        Image(systemName: "magnifyingglass")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 12)

                // Quick filters
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        FilterChip(title: "最近", isSelected: true)
                        FilterChip(title: "最便宜", isSelected: false)
                        FilterChip(title: "有空位", isSelected: false)
                        FilterChip(title: "24小时", isSelected: false)
                    }
                    .padding(.horizontal, 20)
                }

                // Parking list
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(0..<5, id: \.self) { index in
                            ParkingCardView(
                                name: "停车场 \(index + 1)",
                                distance: "\(Int.random(in: 50...500))m",
                                price: "$\(Int.random(in: 3...8))/小时",
                                availability: "\(Int.random(in: 5...50))个空位"
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .frame(height: screenHeight - offset)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
        )
        .offset(y: offset)
        .gesture(
            DragGesture()
                .onChanged { value in
                    isDragging = true
                    let newOffset = offset + value.translation.height

                    // Constrain the offset
                    if newOffset >= minOffset && newOffset <= maxOffset {
                        offset = newOffset
                    } else if newOffset < minOffset {
                        // Add resistance when trying to drag beyond min
                        offset = minOffset + (newOffset - minOffset) * 0.3
                    } else if newOffset > maxOffset {
                        // Add resistance when trying to drag beyond max
                        offset = maxOffset + (newOffset - maxOffset) * 0.3
                    }
                }
                .onEnded { value in
                    isDragging = false

                    // Snap to nearest position
                    let velocity = value.predictedEndTranslation.height
                    let targetOffset: CGFloat

                    if velocity > snapThreshold {
                        // Dragging down - collapse
                        targetOffset = maxOffset
                    } else if velocity < -snapThreshold {
                        // Dragging up - expand
                        targetOffset = minOffset
                    } else {
                        // Snap to nearest
                        let midPoint = (minOffset + maxOffset) / 2
                        targetOffset = offset < midPoint ? minOffset : maxOffset
                    }

                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        offset = targetOffset
                    }
                }
        )
    }
}

// MARK: - Filter Chip
struct FilterChip: View {
    let title: String
    let isSelected: Bool

    var body: some View {
        Text(title)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.blue : Color.gray.opacity(0.2))
            )
            .foregroundColor(isSelected ? .white : .primary)
    }
}

// MARK: - Parking Card View
struct ParkingCardView: View {
    let name: String
    let distance: String
    let price: String
    let availability: String

    var body: some View {
        HStack(spacing: 12) {
            // Parking icon
            Image(systemName: "car.fill")
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 40, height: 40)
                .background(Color.blue.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            // Info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(name)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Spacer()

                    Text(distance)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                HStack {
                    Text(price)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.green)

                    Spacer()

                    Text(availability)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Navigation button
            Button(action: {
                // Navigate action
            }) {
                Image(systemName: "arrow.right.circle.fill")
                    .font(.title3)
                    .foregroundColor(.blue)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}
