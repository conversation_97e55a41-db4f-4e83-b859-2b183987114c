//
//  ContentView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

struct ContentView: View {
    @StateObject private var locationManager = LocationManager()
    @State private var showingLocationAlert = false
    @State private var bottomSheetOffset: CGFloat = 300 // Initial offset (mostly hidden)
    @State private var isDragging = false
    @State private var searchText = ""

    // Bottom sheet positions
    private let minOffset: CGFloat = 100 // Fully expanded
    private let maxOffset: CGFloat = 300 // Mostly collapsed
    private let snapThreshold: CGFloat = 50 // Threshold for snapping

    // Sample parking data
    private let allParkingLocations = [
        ParkingLocation(id: 1, name: "La Jolla Parking Structure", address: "Chesapeake Avenue", price: "$10", spots: "24"),
        ParkingLocation(id: 2, name: "Downtown Parking Garage", address: "Main Street", price: "$15", spots: "18"),
        ParkingLocation(id: 3, name: "Beach Side Parking", address: "Ocean Boulevard", price: "$8", spots: "32"),
        ParkingLocation(id: 4, name: "Shopping Center Lot", address: "Commerce Way", price: "$12", spots: "45"),
        ParkingLocation(id: 5, name: "University Parking", address: "Campus Drive", price: "$6", spots: "67")
    ]

    // Filtered parking locations based on search
    private var filteredParkingLocations: [ParkingLocation] {
        if searchText.isEmpty {
            return Array(allParkingLocations.prefix(3)) // Show first 3 by default
        } else {
            return allParkingLocations.filter { location in
                location.name.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // Main Map View with custom user location
                MapViewRepresentable(locationManager: locationManager)
                    .ignoresSafeArea()
                    .onAppear {
                        // LocationManager will automatically start location updates in init
                        // This is just a fallback in case user manually requests location
                        if locationManager.authorizationStatus == .authorizedWhenInUse ||
                           locationManager.authorizationStatus == .authorizedAlways {
                            locationManager.requestLocation()
                        }
                    }

                // Top Navigation Bar
                VStack {
                    HStack {
                        Text("XYZ Melbourne Parking")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color(.systemBackground).opacity(0.9))
                            .clipShape(RoundedRectangle(cornerRadius: 12))

                        Spacer()

                        // Location Button
                        Button(action: {
                            locationManager.requestLocation()
                        }) {
                            Image(systemName: "location.fill")
                                .font(.title3)
                                .foregroundColor(.blue)
                        }
                        .padding(12)
                        .background(Color(.systemBackground))
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    Spacer()
                }

                // Bottom Sheet
                GeometryReader { geometry in
                    VStack {
                        Spacer()

                        BottomSheetView(
                            offset: $bottomSheetOffset,
                            isDragging: $isDragging,
                            searchText: $searchText,
                            minOffset: minOffset,
                            maxOffset: maxOffset,
                            snapThreshold: snapThreshold,
                            screenHeight: geometry.size.height,
                            parkingLocations: filteredParkingLocations
                        )
                    }
                }
            }
        }
        .alert("Location Access Required", isPresented: $showingLocationAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable location access in Settings to find parking near you.")
        }
        .onChange(of: locationManager.authorizationStatus) { _, status in
            if status == .denied || status == .restricted {
                showingLocationAlert = true
            }
        }
    }
}

#Preview {
    ContentView()
}

// MARK: - MapView UIViewRepresentable
struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var locationManager: LocationManager

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = false // We'll handle this ourselves
        mapView.userTrackingMode = .none
        mapView.showsCompass = true
        mapView.showsScale = true

        // Register custom annotation view
        mapView.register(UserLocationAnnotationView.self,
                        forAnnotationViewWithReuseIdentifier: UserLocationAnnotationView.identifier)

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update map region
        if mapView.region.center.latitude != locationManager.region.center.latitude ||
           mapView.region.center.longitude != locationManager.region.center.longitude {
            mapView.setRegion(locationManager.region, animated: true)
        }

        // Update user location annotation
        if let userAnnotation = locationManager.userLocationAnnotation {
            if !mapView.annotations.contains(where: { $0 === userAnnotation }) {
                mapView.addAnnotation(userAnnotation)
            }

            // Update heading for existing annotation view
            if let annotationView = mapView.view(for: userAnnotation) as? UserLocationAnnotationView {
                annotationView.updateHeading(locationManager.userHeading)
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewRepresentable

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            if annotation is UserLocationAnnotation {
                let annotationView = mapView.dequeueReusableAnnotationView(
                    withIdentifier: UserLocationAnnotationView.identifier,
                    for: annotation
                ) as! UserLocationAnnotationView

                // Update heading immediately
                annotationView.updateHeading(parent.locationManager.userHeading)

                return annotationView
            }
            return nil
        }

        func mapView(_ mapView: MKMapView, didAdd views: [MKAnnotationView]) {
            // Update heading for newly added user location annotation
            for view in views {
                if let userLocationView = view as? UserLocationAnnotationView {
                    userLocationView.updateHeading(parent.locationManager.userHeading)
                }
            }
        }
    }
}

// MARK: - Parking Location Model
struct ParkingLocation: Identifiable {
    let id: Int
    let name: String
    let address: String
    let price: String
    let spots: String
}

// MARK: - Bottom Sheet View
struct BottomSheetView: View {
    @Binding var offset: CGFloat
    @Binding var isDragging: Bool
    @Binding var searchText: String

    let minOffset: CGFloat
    let maxOffset: CGFloat
    let snapThreshold: CGFloat
    let screenHeight: CGFloat
    let parkingLocations: [ParkingLocation]

    var body: some View {
        VStack(spacing: 0) {
            // Handle bar
            RoundedRectangle(cornerRadius: 3)
                .fill(Color.gray.opacity(0.4))
                .frame(width: 40, height: 6)
                .padding(.top, 8)

            // Content
            VStack(spacing: 20) {
                // Search bar
                HStack {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                            .font(.system(size: 16))

                        TextField("Search parking locations...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.gray)
                                    .font(.system(size: 16))
                            }
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(.systemGray6))
                    )
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)

                // Vehicle type selection
                HStack(spacing: 16) {
                    VehicleTypeButton(
                        icon: "car.fill",
                        title: "Car",
                        isSelected: true,
                        backgroundColor: .blue
                    )
                    VehicleTypeButton(
                        icon: "bicycle",
                        title: "Bike",
                        isSelected: false,
                        backgroundColor: .gray
                    )
                    VehicleTypeButton(
                        icon: "bus.fill",
                        title: "Van",
                        isSelected: false,
                        backgroundColor: .gray
                    )
                    VehicleTypeButton(
                        icon: "square.grid.2x2",
                        title: "More",
                        isSelected: false,
                        backgroundColor: .gray
                    )
                }
                .padding(.horizontal, 20)

                // Section title
                HStack {
                    Text("Car")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    Text("Parking Nearby")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    Spacer()
                }
                .padding(.horizontal, 20)

                // Parking list
                ScrollView {
                    LazyVStack(spacing: 16) {
                        if parkingLocations.isEmpty {
                            VStack(spacing: 12) {
                                Image(systemName: "magnifyingglass")
                                    .font(.system(size: 40))
                                    .foregroundColor(.gray)
                                Text("No parking locations found")
                                    .font(.headline)
                                    .foregroundColor(.gray)
                                Text("Try adjusting your search terms")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.top, 40)
                        } else {
                            ForEach(Array(parkingLocations.enumerated()), id: \.element.id) { index, location in
                                ParkingLocationCard(
                                    imageName: "parking_\(location.id)",
                                    title: location.name,
                                    address: location.address,
                                    price: location.price,
                                    spots: location.spots,
                                    isTopCard: index == 0
                                )
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .frame(height: screenHeight - offset)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
        )
        .offset(y: offset)
        .gesture(
            DragGesture()
                .onChanged { value in
                    isDragging = true
                    let newOffset = offset + value.translation.height

                    // Constrain the offset
                    if newOffset >= minOffset && newOffset <= maxOffset {
                        offset = newOffset
                    } else if newOffset < minOffset {
                        // Add resistance when trying to drag beyond min
                        offset = minOffset + (newOffset - minOffset) * 0.3
                    } else if newOffset > maxOffset {
                        // Add resistance when trying to drag beyond max
                        offset = maxOffset + (newOffset - maxOffset) * 0.3
                    }
                }
                .onEnded { value in
                    isDragging = false

                    // Snap to nearest position
                    let velocity = value.predictedEndTranslation.height
                    let targetOffset: CGFloat

                    if velocity > snapThreshold {
                        // Dragging down - collapse
                        targetOffset = maxOffset
                    } else if velocity < -snapThreshold {
                        // Dragging up - expand
                        targetOffset = minOffset
                    } else {
                        // Snap to nearest
                        let midPoint = (minOffset + maxOffset) / 2
                        targetOffset = offset < midPoint ? minOffset : maxOffset
                    }

                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        offset = targetOffset
                    }
                }
        )
    }
}

// MARK: - Vehicle Type Button
struct VehicleTypeButton: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let backgroundColor: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(isSelected ? .white : .gray)
                .frame(width: 50, height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isSelected ? backgroundColor : Color(.systemGray5))
                )

            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .primary : .secondary)
        }
    }
}

// MARK: - Parking Location Card
struct ParkingLocationCard: View {
    let imageName: String
    let title: String
    let address: String
    let price: String
    let spots: String
    let isTopCard: Bool

    var body: some View {
        HStack(spacing: 12) {
            // Parking image
            AsyncImage(url: URL(string: "https://picsum.photos/80/60?random=\(imageName)")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color(.systemGray5))
                    .overlay(
                        Image(systemName: "car.fill")
                            .foregroundColor(.gray)
                    )
            }
            .frame(width: 80, height: 60)
            .clipShape(RoundedRectangle(cornerRadius: 8))

            // Info
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .lineLimit(1)

                HStack(spacing: 4) {
                    Image(systemName: "location")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text(address)
                        .font(.caption)
                        .foregroundColor(.gray)
                }

                HStack(spacing: 16) {
                    HStack(spacing: 2) {
                        Text(price)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                        Text("/hr")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }

                    HStack(spacing: 2) {
                        Text(spots)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(isTopCard ? .orange : .blue)
                        Text("spots")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }

            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}
